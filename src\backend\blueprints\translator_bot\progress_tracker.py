"""
Progress tracking service for translation operations.
"""
import threading
import time
from typing import Dict, Optional
from flask import current_app


class ProgressTracker:
    """Thread-safe progress tracker for translation operations."""
    
    def __init__(self):
        self._sessions: Dict[str, Dict] = {}
        self._lock = threading.Lock()
    
    def create_session(self, session_id: str, total_batches: int, details: str = "") -> None:
        """Create a new progress tracking session."""
        with self._lock:
            self._sessions[session_id] = {
                'total': total_batches,
                'completed': 0,
                'details': details,
                'start_time': time.time(),
                'last_updated': time.time()
            }
            current_app.logger.info(f"Created progress session {session_id} with {total_batches} total batches")
    
    def update_progress(self, session_id: str, completed: int = None, increment: int = None, details: str = None) -> None:
        """Update progress for a session."""
        with self._lock:
            if session_id not in self._sessions:
                current_app.logger.warning(f"Progress session {session_id} not found")
                return
            
            session = self._sessions[session_id]
            
            if completed is not None:
                session['completed'] = completed
            elif increment is not None:
                session['completed'] += increment
            
            if details is not None:
                session['details'] = details
            
            session['last_updated'] = time.time()
            
            current_app.logger.debug(f"Updated progress for {session_id}: {session['completed']}/{session['total']}")
    
    def get_progress(self, session_id: str) -> Optional[Dict]:
        """Get current progress for a session."""
        with self._lock:
            if session_id not in self._sessions:
                return None
            
            session = self._sessions[session_id].copy()
            elapsed_time = time.time() - session['start_time']
            session['elapsed_time'] = elapsed_time
            
            # Calculate ETA if we have progress
            if session['completed'] > 0 and session['completed'] < session['total']:
                avg_time_per_batch = elapsed_time / session['completed']
                remaining_batches = session['total'] - session['completed']
                eta_seconds = avg_time_per_batch * remaining_batches
                session['eta_seconds'] = eta_seconds
            else:
                session['eta_seconds'] = None
            
            return session
    
    def complete_session(self, session_id: str, details: str = "Translation completed") -> None:
        """Mark a session as completed."""
        with self._lock:
            if session_id in self._sessions:
                session = self._sessions[session_id]
                session['completed'] = session['total']
                session['details'] = details
                session['last_updated'] = time.time()
                current_app.logger.info(f"Completed progress session {session_id}")
    
    def remove_session(self, session_id: str) -> None:
        """Remove a progress session."""
        with self._lock:
            if session_id in self._sessions:
                del self._sessions[session_id]
                current_app.logger.info(f"Removed progress session {session_id}")
    
    def cleanup_old_sessions(self, max_age_seconds: int = 3600) -> None:
        """Remove sessions older than max_age_seconds."""
        current_time = time.time()
        with self._lock:
            sessions_to_remove = []
            for session_id, session in self._sessions.items():
                if current_time - session['last_updated'] > max_age_seconds:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self._sessions[session_id]
                current_app.logger.info(f"Cleaned up old progress session {session_id}")


# Global progress tracker instance
progress_tracker = ProgressTracker()
